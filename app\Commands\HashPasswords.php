<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class HashPasswords extends BaseCommand
{
    protected $group       = 'Database';
    protected $name        = 'hash:passwords';
    protected $description = 'Hash existing plain text passwords in the database';

    public function run(array $params)
    {
        $db = \Config\Database::connect();

        CLI::write('Fixing existing plain text passwords...', 'yellow');

        // Get all users with plain text passwords (not starting with $2y$)
        $query = $db->query("SELECT id, email, password FROM users WHERE password NOT LIKE '$2y$%' AND password IS NOT NULL AND password != ''");
        $users = $query->getResultArray();

        CLI::write('Found ' . count($users) . ' users with plain text passwords.', 'cyan');

        foreach ($users as $user) {
            $plainPassword = $user['password'];
            $hashedPassword = password_hash($plainPassword, PASSWORD_DEFAULT);
            
            CLI::write("Updating user ID {$user['id']} ({$user['email']}):", 'green');
            CLI::write("  Old: {$plainPassword}");
            CLI::write("  New: " . substr($hashedPassword, 0, 20) . "...");
            
            $db->query("UPDATE users SET password = ? WHERE id = ?", [$hashedPassword, $user['id']]);
        }

        // Also check dakoii_users table
        $query2 = $db->query("SELECT id, username, password FROM dakoii_users WHERE password NOT LIKE '$2y$%' AND password IS NOT NULL AND password != ''");
        $dakoiiUsers = $query2->getResultArray();

        CLI::write('Found ' . count($dakoiiUsers) . ' dakoii users with plain text passwords.', 'cyan');

        foreach ($dakoiiUsers as $user) {
            $plainPassword = $user['password'];
            $hashedPassword = password_hash($plainPassword, PASSWORD_DEFAULT);
            
            CLI::write("Updating dakoii user ID {$user['id']} ({$user['username']}):", 'green');
            CLI::write("  Old: {$plainPassword}");
            CLI::write("  New: " . substr($hashedPassword, 0, 20) . "...");
            
            $db->query("UPDATE dakoii_users SET password = ? WHERE id = ?", [$hashedPassword, $user['id']]);
        }

        CLI::write('Password hashing completed!', 'green');
    }
}
